
#tooltip {
  position: absolute;
  /* width: 200px; */
  white-space: nowrap;
  height: auto;
  padding: 10px;
  background-color: #e0dbd8;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  border-radius: 10px;
  -webkit-box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.4);
  -mox-box-shadow: 4px 4px 4px 10px rgba(0, 0, 0, 0.4);
  box-shadow: 4px 4px 10px rbga(0, 0, 0, 0.4);
  pointer-events: none;
}

#tooltip {
  display: none;
}

#tooltip p {
  margin: 0;
  font-family: sans-serif;
  font-size: 16px;
  line-height: 20px;
}

.chart-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.modal-yesno{
 display: flex;
 align-items: center;
 justify-content: center;
}

.cross-button{
 border-radius: 30px;
 position: absolute;
 float: left;
 background-color: #000;
 color: white;
 top: -10px;
 right: -10px;
 cursor: pointer;
 z-index: 1000;
}
